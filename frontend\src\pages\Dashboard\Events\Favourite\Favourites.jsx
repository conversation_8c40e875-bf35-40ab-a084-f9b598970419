import { Grid } from "@mui/material";
import { useEffect, useState, memo, useRef } from "react";
import axiosInstance from "../../../../axios";
import DetailModal from "../DetailModal";
import { useParams } from "react-router-dom";
import theme from "../../../../theme";
import { useUser } from "../../../../hooks/UserHook";
import { getSocket } from "../../../../socket";
import VirtualizedCardList from "../VirtualizedCardList";

const Favourites = ({ vessels }) => {
    const { id } = useParams();
    const { user } = useUser();
    const [events, setEvents] = useState([]);
    const [filteredEvents, setFilteredEvents] = useState([]);
    const [isLoading, setIsLoading] = useState(true);
    const [showDetailModal, setShowDetailModal] = useState(false);
    const [selectedCard, setSelectedCard] = useState(null);
    const [favouriteArtifacts, setFavouriteArtifacts] = useState([]);
    const virtualizedListContainerRef = useRef();

    const fetchArtifacts = async (isSocketTriggered = false) => {
        if (!isSocketTriggered) {
            setIsLoading(true);
        }
        try {
            const { artifacts, favourites } = await axiosInstance
                .get(`/artifactFavourites/${user._id}`)
                .then((res) => res.data)
                .catch((err) => {
                    console.error(`Error fetching artifacts in Events`, err);
                });

            setFavouriteArtifacts(favourites);

            setEvents(artifacts);
            setIsLoading(false);
        } catch (err) {
            setIsLoading(false);
            console.error(`Error fetching favourites artifacts in Events`, err);
        }
    };

    useEffect(() => {
        const unidIds = vessels.map((v) => v.unit_id);
        setFilteredEvents(events.filter((e) => unidIds.includes(e.unit_id)));
    }, [events]);

    useEffect(() => {
        fetchArtifacts();
    }, [id]);

    useEffect(() => {
        const socket = getSocket();
        socket.on("favourites/changed", () => fetchArtifacts(true));

        return () => {
            socket.off("favourites/changed", () => fetchArtifacts(true));
        };
    }, []);

    return (
        <Grid container color={"#FFFFFF"} flexDirection={"column"} height={"100%"}>
            <Grid
                container
                overflow={"auto"}
                display={"block"}
                border={`1px solid ${theme.palette.custom.borderColor}`}
                borderRadius={"10px"}
                padding={"10px 24px"}
                size="grow"
            >
                <Grid container height={"100%"} overflow={"auto"} ref={virtualizedListContainerRef}>
                    <VirtualizedCardList
                        events={filteredEvents}
                        setShowDetailModal={setShowDetailModal}
                        setSelectedCard={setSelectedCard}
                        favouriteArtifacts={favouriteArtifacts}
                        vessels={vessels}
                        isLoading={isLoading}
                        containerRef={virtualizedListContainerRef}
                    />
                </Grid>
            </Grid>
            <DetailModal
                showDetailModal={showDetailModal}
                setShowDetailModal={setShowDetailModal}
                selectedCard={selectedCard}
                setSelectedCard={setSelectedCard}
                id={id}
                favouriteArtifacts={favouriteArtifacts}
            />
        </Grid>
    );
};

export default memo(Favourites);
