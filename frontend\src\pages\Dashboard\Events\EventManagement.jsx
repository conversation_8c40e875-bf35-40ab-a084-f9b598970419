import { Grid, InputAdornment, OutlinedInput, Tab, Tabs, Button } from "@mui/material";
import { useEffect, useMemo, useRef, useState, memo } from "react";
import { Search } from "@mui/icons-material";
import { useApp } from "../../../hooks/AppHook";
import { useUser } from "../../../hooks/UserHook";
import Events from "./Events";
import theme from "../../../theme";
import axiosInstance from "../../../axios.js";
import Favourites from "./Favourite/Favourites.jsx";
import { isEnvironment } from "../../../utils.js";
import environment from "../../../../environment.js";
import { useLocation } from "react-router-dom";
import useVesselInfo from "../../../hooks/VesselInfoHook.jsx";

const EventsManagement = () => {
    const { user } = useUser();
    const { vesselInfo, fetchVesselsInfo } = useVesselInfo();
    const [tab, setTab] = useState("");
    const [searchQuery, setSearchQuery] = useState("");
    const debounced = useRef(null);
    const [searchResults, setSearchResults] = useState({});
    const [hasVisitedEvents, setHasVisitedEvents] = useState(false);
    const location = useLocation();

    const [showFilterModal, setShowFilterModal] = useState(false);
    const [vessels, setVessels] = useState([]);

    const { isMobile, devMode } = useApp();

    useEffect(() => {
        const isEventPath = location.pathname === "/dashboard/events" || location.pathname.startsWith("/dashboard/events");

        if (isEventPath) {
            setHasVisitedEvents(true);
        }
    }, [location]);

    const tabs = useMemo(
        () => [
            {
                value: "events",
                label: "Events",
                component: vessels.length > 0 && (
                    <Events
                        showFilterModal={showFilterModal}
                        setShowFilterModal={setShowFilterModal}
                        searchFilters={searchResults}
                        vessels={vessels}
                        tab={tab}
                    />
                ),
                display: true,
            },
            {
                value: "favourites",
                label: "Favorites",
                component: vessels.length > 0 && <Favourites vessels={vessels} />,
                display: true,
            },
        ],
        [user, showFilterModal, searchResults, vessels, tab],
    );

    const filterVessels = (vessels) => {
        if (!vessels || !Array.isArray(vessels)) return [];

        return vessels.filter((vessel) => {
            if (!vessel.unit_id) return false;
            if (vessel.is_active === false && !devMode) return false;
            return true;
        });
    };

    const fetchVessels = async () => {
        try {
            if (vesselInfo) {
                const filteredVessels = filterVessels(vesselInfo);
                setVessels(filteredVessels);
            } else {
                fetchVesselsInfo();
            }
        } catch (err) {
            console.error("An error occurred while fetching vessels on the events Page:", err);
        }
    };

    useEffect(() => {
        fetchVessels();
    }, [vesselInfo]);

    useEffect(() => {
        if (!tab) {
            setTab(tabs.find((t) => t.display)?.value || "");
        }
    }, [tabs]);

    const handleSearchChange = (event) => {
        setSearchQuery(event.target.value);
    };

    useEffect(() => {
        if (debounced.current) {
            clearTimeout(debounced.current);
        }
        debounced.current = setTimeout(async () => {
            if (searchQuery) {
                try {
                    const response = await axiosInstance.post("/completions", {
                        text: searchQuery,
                    });
                    for (let key in response.data) {
                        if (response.data[key] === null || response.data[key].length === 0) {
                            delete response.data[key];
                        }
                    }
                    setSearchResults(response.data);
                } catch (error) {
                    console.error(error);
                }
            } else {
                setSearchResults({});
            }
        }, 600);
    }, [searchQuery]);

    const handleTabChange = (event, newValue) => {
        setTab(newValue);
    };

    if (!hasVisitedEvents) {
        return null;
    }

    return (
        user &&
        tabs.some((t) => t.display) &&
        tab && (
            <Grid
                container
                color={"#FFFFFF"}
                flexDirection={"column"}
                width={"100%"}
                height={"100%"}
                overflow={"auto"}
                sx={{ backgroundColor: theme.palette.custom.darkBlue }}
            >
                <Grid
                    container
                    padding={2}
                    display={"flex"}
                    columnGap={{ xs: 2, lg: 0 }}
                    rowGap={2}
                    justifyContent={"space-between"}
                    alignItems={"center"}
                    flexWrap={"wrap"}
                >
                    <Grid
                        size={{
                            xs: "grow",
                            lg: 4.5,
                        }}
                    >
                        <Tabs
                            value={tab}
                            onChange={handleTabChange}
                            sx={{
                                width: "100%",
                                padding: "4px",
                                border: `2px solid ${theme.palette.custom.borderColor}`,
                                borderRadius: "8px",
                                backgroundColor: "transparent",
                                "& .MuiTabs-flexContainer": {
                                    height: "100%",
                                },
                                "& .MuiButtonBase-root": {
                                    width: "50%",
                                    borderRadius: "8px",
                                },
                                "& .MuiButtonBase-root.Mui-selected": {
                                    backgroundColor: theme.palette.custom.mainBlue,
                                },
                            }}
                        >
                            {tabs
                                .filter((t) => t.display)
                                .map((t) => (
                                    <Tab
                                        key={t.value}
                                        label={t.label}
                                        value={t.value}
                                        sx={{
                                            maxWidth: "none",
                                        }}
                                    />
                                ))}
                        </Tabs>
                    </Grid>
                    {tab === "events" && (
                        <Grid
                            container
                            columnGap={2}
                            justifyContent={"space-between"}
                            size={{
                                xs: 12,
                                lg: 7.4,
                            }}
                        >
                            <Grid
                                size={{
                                    xs: "grow",
                                    lg: 5.8,
                                }}
                            >
                                <OutlinedInput
                                    type="text"
                                    value={searchQuery}
                                    onChange={handleSearchChange}
                                    startAdornment={
                                        <InputAdornment position="start">
                                            <Search sx={{ color: "#FFFFFF" }} />
                                        </InputAdornment>
                                    }
                                    placeholder="Search (Powered by AI) "
                                    sx={{
                                        color: "#FFFFFF",
                                        width: "100%",
                                        "& .MuiOutlinedInput-notchedOutline": {
                                            border: "2px solid",
                                            borderColor: theme.palette.custom.borderColor + " !important",
                                            borderRadius: "8px",
                                        },
                                        display: isEnvironment(environment.stagingAndProduction) ? "none" : "flex",
                                    }}
                                />
                            </Grid>
                            <Grid alignItems={"center"} display={"flex"} justifyContent={"flex-end"} gap={2} size="auto">
                                <Button
                                    className="events-step-1"
                                    variant="outlined"
                                    startIcon={<img src={"/icons/filter_icon.svg"} width={20} height={20} alt={"Filter"} />}
                                    sx={{
                                        "&.MuiButtonBase-root": {
                                            borderColor: theme.palette.custom.borderColor,
                                            height: { xs: "100%", lg: "auto" },
                                            color: "#FFFFFF",
                                            padding: { xs: "0", lg: "10px 20px" },
                                            fontWeight: "bold",
                                        },
                                        "& .MuiButton-icon": {
                                            marginRight: { xs: 0, lg: "10px" },
                                        },
                                    }}
                                    onClick={() => setShowFilterModal(true)}
                                >
                                    {!isMobile && "Filter"}
                                </Button>
                            </Grid>
                        </Grid>
                    )}
                </Grid>
                {tabs
                    .filter((t) => t.display)
                    .map((t) => (
                        <Grid key={t.value} display={tab !== t.value && "none"} paddingX={2} paddingBottom={2} width={"100%"} size="grow">
                            {t.component}
                        </Grid>
                    ))}
            </Grid>
        )
    );
};

export default memo(EventsManagement);
